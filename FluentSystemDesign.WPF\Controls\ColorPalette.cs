using System;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace FluentSystemDesign.WPF.Controls
{
    /// <summary>
    /// 色彩调色板控件，用于展示色彩系统
    /// </summary>
    public class ColorPalette : Control
    {
        static ColorPalette()
        {
            DefaultStyleKeyProperty.OverrideMetadata(typeof(ColorPalette), 
                new FrameworkPropertyMetadata(typeof(ColorPalette)));
        }

        /// <summary>
        /// 调色板标题
        /// </summary>
        public string Title
        {
            get { return (string)GetValue(TitleProperty); }
            set { SetValue(TitleProperty, value); }
        }

        public static readonly DependencyProperty TitleProperty =
            DependencyProperty.Register("Title", typeof(string), typeof(ColorPalette), 
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 色彩项集合
        /// </summary>
        public ColorItemCollection ColorItems
        {
            get { return (ColorItemCollection)GetValue(ColorItemsProperty); }
            set { SetValue(ColorItemsProperty, value); }
        }

        public static readonly DependencyProperty ColorItemsProperty =
            DependencyProperty.Register("ColorItems", typeof(ColorItemCollection), typeof(ColorPalette), 
                new PropertyMetadata(null));

        public ColorPalette()
        {
            ColorItems = new ColorItemCollection();
        }
    }

    /// <summary>
    /// 色彩项
    /// </summary>
    public class ColorItem : DependencyObject
    {
        /// <summary>
        /// 色彩名称
        /// </summary>
        public string Name
        {
            get { return (string)GetValue(NameProperty); }
            set { SetValue(NameProperty, value); }
        }

        public static readonly DependencyProperty NameProperty =
            DependencyProperty.Register("Name", typeof(string), typeof(ColorItem), 
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 色彩值
        /// </summary>
        public Color Color
        {
            get { return (Color)GetValue(ColorProperty); }
            set { SetValue(ColorProperty, value); }
        }

        public static readonly DependencyProperty ColorProperty =
            DependencyProperty.Register("Color", typeof(Color), typeof(ColorItem), 
                new PropertyMetadata(Colors.Transparent));

        /// <summary>
        /// 色彩画刷
        /// </summary>
        public Brush Brush
        {
            get { return (Brush)GetValue(BrushProperty); }
            set { SetValue(BrushProperty, value); }
        }

        public static readonly DependencyProperty BrushProperty =
            DependencyProperty.Register("Brush", typeof(Brush), typeof(ColorItem), 
                new PropertyMetadata(null));

        /// <summary>
        /// 十六进制色值
        /// </summary>
        public string HexValue
        {
            get { return (string)GetValue(HexValueProperty); }
            set { SetValue(HexValueProperty, value); }
        }

        public static readonly DependencyProperty HexValueProperty =
            DependencyProperty.Register("HexValue", typeof(string), typeof(ColorItem), 
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 是否为深色
        /// </summary>
        public bool IsDark
        {
            get { return (bool)GetValue(IsDarkProperty); }
            set { SetValue(IsDarkProperty, value); }
        }

        public static readonly DependencyProperty IsDarkProperty =
            DependencyProperty.Register("IsDark", typeof(bool), typeof(ColorItem), 
                new PropertyMetadata(false));

        public ColorItem()
        {
            // 当颜色改变时，自动更新相关属性
            DependencyPropertyDescriptor.FromProperty(ColorProperty, typeof(ColorItem))
                .AddValueChanged(this, OnColorChanged);
        }

        private void OnColorChanged(object sender, EventArgs e)
        {
            Brush = new SolidColorBrush(Color);
            HexValue = $"#{Color.R:X2}{Color.G:X2}{Color.B:X2}";
            
            // 计算是否为深色（使用相对亮度公式）
            var luminance = (0.299 * Color.R + 0.587 * Color.G + 0.114 * Color.B) / 255;
            IsDark = luminance < 0.5;
        }
    }

    /// <summary>
    /// 色彩项集合
    /// </summary>
    public class ColorItemCollection : System.Collections.ObjectModel.ObservableCollection<ColorItem>
    {
    }
}
